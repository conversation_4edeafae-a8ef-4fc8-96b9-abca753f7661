import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/constants/enum_filter_product.dart';
import 'package:shopping/pages/package/widgets/filter_endraw_widget.dart';
import 'package:shopping/pages/sim/step_pick_product_controller.dart';
import 'package:shopping/widgets/multi_button.dart';

class FilterProductWithSimEndrawWidget extends StatelessWidget {
  const FilterProductWithSimEndrawWidget({super.key, required this.controller});
  final StepPickProductController controller;
  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: AppColors.white,
      width: (MediaQuery.of(context).size.width) * 0.9,
      child: Safe<PERSON>rea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: () {
                  Navigator.of(context).pop();
                },
                child: Align(
                    alignment: Alignment.centerRight,
                    child: SvgPicture.asset(SvgPath.svgIconClose)),
              ),
              const SizedBox(height: 10),
              Row(
                children: [
                  SvgPicture.asset(
                    SvgPath.svgIconFilter,
                    height: 24,
                    width: 24,
                    color: AppColors.redFF6540,
                  ),
                  const SizedBox(width: 13),
                  Text("Bộ lọc", style: AppTextStyle.s18Bold)
                ],
              ),
              const SizedBox(height: 17),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Chu kỳ gói cước",
                        style: AppTextStyle.s16SemiBold,
                      ),
                      const SizedBox(height: 5),
                      GetBuilder<StepPickProductController>(
                        id: StepPickProductControllerUpdateKey
                            .updateListCycleType,
                        builder: (_) => ListView.builder(
                          physics: NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          itemCount: FilterCondition.listCycleType.length,
                          itemBuilder: (context, index) {
                            return GestureDetector(
                              onTap: () => controller.onSelectFilterCycle(
                                  FilterCondition.listCycleType[index]),
                              child: CheckBoxLineWidget(
                                title: FilterCondition
                                    .listCycleType[index].nameCycleType,
                                isSelected: controller.isFilterCycleSelected(
                                    FilterCondition.listCycleType[index]),
                                onChanged: (p0) =>
                                    controller.onSelectFilterCycle(
                                        FilterCondition.listCycleType[index]),
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        "Khoảng giá",
                        style: AppTextStyle.s16SemiBold,
                      ),
                      const SizedBox(height: 5),
                      GetBuilder<StepPickProductController>(
                        id: StepPickProductControllerUpdateKey
                            .updateListPriceType,
                        builder: (_) => ListView.builder(
                          physics: NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          itemCount: FilterCondition.listPriceFromType.length,
                          itemBuilder: (context, index) {
                            return GestureDetector(
                              onTap: () => controller.onSelectFilterPrice(
                                  FilterCondition.listPriceFromType[index]),
                              child: CheckBoxLineWidget(
                                title: FilterCondition
                                    .listPriceFromType[index].namePriceFrom,
                                isSelected: controller.isFilterPriceSelected(
                                    FilterCondition.listPriceFromType[index]),
                                onChanged: (p0) => controller
                                    .onSelectFilterPrice(FilterCondition
                                        .listPriceFromType[index]),
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        "Sắp xếp",
                        style: AppTextStyle.s16SemiBold,
                      ),
                      GetBuilder<StepPickProductController>(
                          id: StepPickProductControllerUpdateKey
                              .updateOrderByType,
                          builder: (_) => ListView.builder(
                                physics: NeverScrollableScrollPhysics(),
                                shrinkWrap: true,
                                itemCount:
                                    FilterCondition.listOrderByType.length,
                                itemBuilder: (context, index) {
                                  return GestureDetector(
                                    onTap: () => controller
                                        .onSelectFilterOrderBy(FilterCondition
                                            .listOrderByType[index]),
                                    child: RadioBtnLineWidget(
                                      item: FilterCondition
                                          .listOrderByType[index],
                                      groupValue: controller.orderByTypeChoosed,
                                      onChanged: (p0) => controller
                                          .onSelectFilterOrderBy(FilterCondition
                                              .listOrderByType[index]),
                                    ),
                                  );
                                },
                              ))
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 8),
              MultiButton(
                textLeft: 'THIẾT LẬP LẠI',
                textRight: 'TIẾP TỤC',
                onTapLeft: controller.onResetFilter,
                onTapRight: () {
                  controller.onSubmitFilter();
                  Navigator.pop(context);
                },
              ),
              const SizedBox(height: 25)
            ],
          ),
        ),
      ),
    );
  }
}
