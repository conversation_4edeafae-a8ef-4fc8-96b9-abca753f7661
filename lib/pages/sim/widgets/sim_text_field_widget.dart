import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/enum/sex_enum.dart';
import 'package:shopping/models/pgd_address.dart';
import 'package:shopping/utils/extension/string_extension.dart';
import 'package:shopping/widgets/custom_text_field.dart';

import '../../../models/province.dart';

class SIMPickerWidget extends StatelessWidget {
  const SIMPickerWidget({
    super.key,
    this.hintText,
    this.title,
    this.isRequire,
    this.listData,
    this.pickData,
    this.value,
    this.disableSelect,
  });

  final String? title;
  final String? hintText;
  final bool? isRequire;
  final List<dynamic>? listData;
  final Function(dynamic)? pickData;
  final dynamic value;
  final bool? disableSelect;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          title != null
              ? LableWidget(
                  isRequired: isRequire,
                  title: title,
                )
              : const SizedBox.shrink(),
          _DropdownListPicker(
            title: title,
            listData: listData,
            pickData: pickData,
            hintText: hintText,
            selectedValue: value,
            disableSelect: disableSelect ?? true,
            isChildren: true,
          ),
          const SizedBox(height: 15)
        ],
      ),
    );
  }
}

class SIMTextFieldWidget extends StatefulWidget {
  const SIMTextFieldWidget(
      {super.key,
      this.title,
      this.hintText,
      this.isRequired,
      this.onSubmitted,
      this.text,
      this.readOnly,
      this.onChanged,
      this.isDate,
      this.pickDate,
      this.errorText,
      this.maxLength,
      this.keyBoardType,
      this.inputBorder,
      this.colorFill,
      this.filled,
      this.initDate,
      this.firstdate,
      this.lastDate,
      this.datePickerEntryMode,
      this.contentPadding});

  final int? maxLength;
  final String? title;
  final bool? isRequired;
  final String? hintText;
  final Function(String)? onSubmitted;
  final Function(String)? onChanged;
  final String? text;
  final bool? readOnly;
  final bool? isDate;
  final Function(DateTime)? pickDate;
  final String? errorText;
  final TextInputType? keyBoardType;
  final InputBorder? inputBorder;
  final Color? colorFill;
  final bool? filled;
  final DateTime? initDate;
  final DateTime? firstdate;
  final DateTime? lastDate;
  final DatePickerEntryMode? datePickerEntryMode;
  final EdgeInsetsGeometry? contentPadding;

  @override
  State<SIMTextFieldWidget> createState() => _SIMTextFieldWidgetState();
}

class _SIMTextFieldWidgetState extends State<SIMTextFieldWidget> {
  late TextEditingController controller;
  @override
  void initState() {
    controller = TextEditingController();
    checkText();
    super.initState();
  }

  @override
  void didUpdateWidget(mode) {
    super.didUpdateWidget(mode);
    // controller.selection = TextSelection.fromPosition(TextPosition(offset: controller.text.length));
    if (mode.text != widget.text) {
      checkText();
      // controller.selection = TextSelection.collapsed(offset: controller.text.length);
    }
  }

  void checkText() {
    setState(() {
      if (widget.text != '' || widget.text != null) {
        controller = TextEditingController(text: widget.text);
        controller.selection = TextSelection.fromPosition(
            TextPosition(offset: controller.text.length));
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        LableWidget(
          title: widget.title,
          isRequired: widget.isRequired,
        ),
        SizedBox(
          // height: 50,
          child: CustomTextField(
            filled: widget.filled ?? false,
            colorFill: widget.colorFill ?? Colors.white,
            hinText: widget.hintText,
            inputBorder: widget.inputBorder,
            keyBoardType: widget.keyBoardType ?? TextInputType.text,
            maxLength: widget.maxLength,
            contentPadding: widget.contentPadding ??
                EdgeInsets.symmetric(horizontal: 20, vertical: 13),
            controller: controller,
            errorText: widget.errorText,
            onSubmitted: widget.onSubmitted,
            onChanged: widget.onChanged,
            inputFormatters: [
              LengthLimitingTextInputFormatter(widget.maxLength)
            ],
            suffixIcon: widget.isDate == true
                ? InkWell(
                    onTap: () async {
                      final date = await showDatePicker(
                          context: context,
                          initialDate: widget.initDate ??
                              ((widget.text?.isNotEmpty == true &&
                                      widget.text?.validateDate() == true)
                                  ? DateFormat('dd/MM/yyyy')
                                      .parse(widget.text ?? '')
                                  : DateTime.now()),
                          firstDate: widget.firstdate ?? DateTime(1950),
                          lastDate: widget.lastDate ?? DateTime(2100),
                          initialEntryMode: widget.datePickerEntryMode ??
                              DatePickerEntryMode.calendar);
                      if (date != null) {
                        widget.pickDate?.call(date);
                      }
                    },
                    child: Container(
                      margin: EdgeInsets.all(15),
                      width: 20,
                      child: SvgPicture.asset(SvgPath.svgPickCalendart),
                    ),
                  )
                : const SizedBox.shrink(),
            autoCorrect: false,
            autoFocus: false,
            colorHint: AppColors.greyF5F6F9,
            colorText: AppColors.black2E2E2E,
            radius: 8,
            fontSize: 15,
            readOnly: widget.readOnly,
            maxLenght: widget.maxLength,
            style:
                AppTextStyle.s15SemiBold.copyWith(color: AppColors.black1E1E1E),
          ),
        ),
      ],
    );
  }
}

class SIMTextFieldWidgetOnlyNumber extends StatefulWidget {
  const SIMTextFieldWidgetOnlyNumber({
    super.key,
    this.title,
    this.hintText,
    this.isRequired,
    this.onSubmitted,
    this.text,
    this.readOnly,
    this.onChanged,
    this.isDate,
    this.pickDate,
    this.errorText,
    this.maxLength,
    this.keyBoardType,
    this.inputBorder,
    this.contentPadding,
  });

  final int? maxLength;
  final String? title;
  final bool? isRequired;
  final String? hintText;
  final Function(String)? onSubmitted;
  final Function(String)? onChanged;
  final String? text;
  final bool? readOnly;
  final bool? isDate;
  final Function(DateTime)? pickDate;
  final String? errorText;
  final TextInputType? keyBoardType;
  final InputBorder? inputBorder;
  final EdgeInsetsGeometry? contentPadding;

  @override
  State<SIMTextFieldWidgetOnlyNumber> createState() =>
      _SIMTextFieldWidgetOnlyNumberState();
}

class _SIMTextFieldWidgetOnlyNumberState
    extends State<SIMTextFieldWidgetOnlyNumber> {
  late TextEditingController controller;
  @override
  void initState() {
    controller = TextEditingController();
    checkText();
    super.initState();
  }

  @override
  void didUpdateWidget(mode) {
    super.didUpdateWidget(mode);
    if (mode.text != widget.text) {
      checkText();
    }
  }

  void checkText() {
    setState(() {
      if (widget.text != '' || widget.text != null) {
        controller = TextEditingController(text: widget.text);
        controller.selection = TextSelection.fromPosition(
            TextPosition(offset: controller.text.length));
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        LableWidget(
          title: widget.title,
          isRequired: widget.isRequired,
        ),
        SizedBox(
          // height: 50,
          child: CustomTextField(
            //check pase input number
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d+([.,]\d*)?$')),
              // FilteringTextInputFormatter.digitsOnly
            ],
            hinText: widget.hintText,
            inputBorder: widget.inputBorder,
            keyBoardType: widget.keyBoardType ?? TextInputType.text,
            maxLength: widget.maxLength,
            contentPadding: widget.contentPadding ??
                EdgeInsets.symmetric(horizontal: 20, vertical: 13),
            controller: controller,
            errorText: widget.errorText,
            onSubmitted: widget.onSubmitted,
            onChanged: widget.onChanged,
            colorFill: (widget.readOnly == true) ? AppColors.greyF5F5F5 : null,
            filled: true,
            suffixIcon: widget.isDate == true
                ? InkWell(
                    onTap: () async {
                      if (widget.readOnly == true) {
                        return;
                      }
                      final date = await showDatePicker(
                        context: context,
                        initialDate: widget.text?.isNotEmpty == true
                            ? DateFormat('dd/MM/yyyy').parse(widget.text ?? '')
                            : DateTime.now(),
                        firstDate: DateTime(1950),
                        lastDate: DateTime(2100),
                      );
                      if (date != null) {
                        widget.pickDate?.call(date);
                      }
                    },
                    child: Container(
                      margin: EdgeInsets.all(12),
                      width: 14,
                      child: SvgPicture.asset(SvgPath.svgPickCalendart),
                    ),
                  )
                : const SizedBox.shrink(),
            autoCorrect: false,
            autoFocus: false,
            colorHint: AppColors.greyF5F6F9,
            colorText: AppColors.black2E2E2E,
            radius: 8,
            fontSize: 15,
            readOnly: widget.readOnly,
            maxLenght: widget.maxLength,
            style: AppTextStyle.s14SemiBold.copyWith(color: AppColors.black),
          ),
        ),
      ],
    );
  }
}

class SIMTextFielNoLabledWidget extends StatefulWidget {
  const SIMTextFielNoLabledWidget({
    super.key,
    this.hintText,
    this.isRequired,
    this.onSubmitted,
    this.text,
    this.readOnly,
    this.onChanged,
  });

  final bool? isRequired;
  final String? hintText;
  final Function(String)? onSubmitted;
  final Function(String)? onChanged;
  final String? text;
  final bool? readOnly;

  @override
  State<SIMTextFielNoLabledWidget> createState() =>
      _SIMTextFieldNoLableWidgetState();
}

class _SIMTextFieldNoLableWidgetState extends State<SIMTextFielNoLabledWidget> {
  late TextEditingController controller;
  @override
  void initState() {
    controller = TextEditingController();
    checkText();
    super.initState();
  }

  @override
  void didUpdateWidget(mode) {
    super.didUpdateWidget(mode);
    if (mode.text != widget.text) {
      checkText();
    }
  }

  void checkText() {
    setState(() {
      if (widget.text != '' || widget.text != null) {
        controller = TextEditingController(text: widget.text);
        controller.selection = TextSelection.fromPosition(
            TextPosition(offset: controller.text.length));
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
            // height: 50,
            child: CustomTextField(
          contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
          controller: controller,
          inputFormatters: [
            LengthLimitingTextInputFormatter(200),
          ],
          // onSubmitted: onSubmitted,
          onChanged: widget.onChanged,
          hinText: widget.hintText,
          autoCorrect: false,
          autoFocus: false,
          colorHint: AppColors.greyF5F6F9,
          colorText: AppColors.black2E2E2E,
          radius: 8,
          fontSize: 15,
          style: AppTextStyle.s15SemiBold.copyWith(
            color: AppColors.black,
          ),
          readOnly: widget.readOnly,
          maxLenght: 200,
        ))
      ],
    );
  }
}

class SIMNoLableWidget extends StatelessWidget {
  const SIMNoLableWidget(
      {super.key,
      this.hintText,
      required this.isRequire,
      required this.listData,
      required this.pickData,
      this.value,
      required this.disableSelect,
      required this.isChildren});

  final String? hintText;
  final bool isRequire;
  final List<dynamic> listData;
  final Function(dynamic)? pickData;
  final dynamic value;
  final bool disableSelect;
  final bool isChildren;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _DropdownListPicker(
          listData: listData,
          pickData: pickData,
          hintText: hintText,
          selectedValue: value,
          disableSelect: disableSelect,
          isChildren: isChildren,
        ),
        const SizedBox(height: 15)
      ],
    );
  }
}

class LableWidget extends StatelessWidget {
  const LableWidget({super.key, this.isRequired, this.title});

  final String? title;
  final bool? isRequired;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        RichText(
          text: TextSpan(children: <TextSpan>[
            TextSpan(
              text: title,
              style: AppTextStyle.s14Medium.copyWith(
                color: AppColors.black,
              ),
            ),
            TextSpan(
              text: (isRequired ?? false) ? ' *' : '',
              style: AppTextStyle.s14Medium.copyWith(
                color: AppColors.redFF0000,
              ),
            ),
          ]),
        ),
        const SizedBox(height: 10)
      ],
    );
  }
}

class _DropdownListPicker extends StatefulWidget {
  _DropdownListPicker(
      {this.listData,
      this.pickData,
      this.selectedValue,
      this.hintText,
      required this.disableSelect,
      required this.isChildren,
      this.title});

  final List<dynamic>? listData;
  final Function(dynamic)? pickData;
  final String? hintText;
  final dynamic selectedValue;
  final bool disableSelect;
  final bool isChildren;
  final String? title;

  @override
  State<_DropdownListPicker> createState() => _DropdownListPickerState();
}

class _DropdownListPickerState extends State<_DropdownListPicker> {
  dynamic initValue;

  @override
  void initState() {
    initValue = widget.selectedValue;
    if (initValue != null) {
      if (initValue is Address) {
        initValue = widget.listData?.firstWhere(
          (element) => element.id == widget.selectedValue.id,
        );
      }
      if (initValue is PhongGiaoDich) {
        initValue = widget.listData?.firstWhere(
          (element) => element.idPgd == widget.selectedValue.idPgd,
        );
      }
    }
    super.initState();
  }

  @override
  void didUpdateWidget(mode) {
    if (mode.selectedValue != widget.selectedValue) {
      initValue = widget.selectedValue;
      if (initValue is Address) {
        initValue = widget.listData?.firstWhere(
          (element) => element.id == widget.selectedValue.id,
        );
      }
      if (initValue is PhongGiaoDich) {
        initValue = widget.listData?.firstWhere(
          (element) => element.idPgd == widget.selectedValue.idPgd,
        );
      }
    }
    super.didUpdateWidget(mode);
  }

  @override
  Widget build(BuildContext context) {
    TextStyle defaultStyle =
        AppTextStyle.s15SemiBold.copyWith(color: AppColors.black1E1E1E);
    return DropdownButtonFormField<dynamic>(
      dropdownColor: Colors.white,
      menuMaxHeight: 200,
      value: initValue,
      style: AppTextStyle.s15SemiBold.copyWith(color: AppColors.black1E1E1E),
      items: widget.listData
          ?.map((e) => DropdownMenuItem(
              enabled: (e is PhongGiaoDich && e.idPgd == -10) ||
                      (e is Address && e.id == -20)
                  ? false
                  : true,
              value: e,
              child: Builder(builder: (context) {
                if (e is Address || e is PhongGiaoDich) {
                  return Text(
                    e.name,
                    style: defaultStyle,
                  );
                } else if (e is SexEnum) {
                  return Text(
                    e.name,
                    style: defaultStyle,
                  );
                } else {
                  return Text(
                    e ?? '',
                    style: defaultStyle,
                  );
                }
              })))
          .toList(),
      onChanged: (value) {
        if (value is PhongGiaoDich && value.idPgd == -10) {
          return;
        } else if (value is Address && value.id == -20) {
          return;
        } else {
          widget.pickData?.call(value);
        }
      },
      hint: Text(
        widget.hintText ?? '',
        style: AppTextStyle.s14Medium.copyWith(
          color: AppColors.black1E1E1E,
        ),
      ),
      isExpanded: true,
      autofocus: true,
      icon: SvgPicture.asset(
        SvgPath.svgArrowDown,
        color: AppColors.black,
      ),
      iconSize: 20,
      elevation: 1,
      validator: (value) => value == null
          ? 'Vui lòng chọn ${widget.title}'
          : (value is PhongGiaoDich && value.idPgd == -10) ||
                  (value is Address && value.id == -20)
              ? 'Vui lòng chọn ${widget.title}'
              : null,
      decoration: InputDecoration(
        fillColor: Colors.white,
        filled: true,
        border: const OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.greyD9D9D9, width: 1.0),
          borderRadius: BorderRadius.all(
            Radius.circular(10.0),
          ),
        ),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.greyD9D9D9, width: 1.0),
          borderRadius: BorderRadius.all(
            Radius.circular(10.0),
          ),
        ),
        enabledBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.greyD9D9D9, width: 1.0),
          borderRadius: BorderRadius.all(
            Radius.circular(10.0),
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
            borderSide: const BorderSide(width: 1, color: Colors.red),
            borderRadius: BorderRadius.circular(10)),
        errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(width: 1, color: Colors.red),
            borderRadius: BorderRadius.circular(10)),
      ),
    );
  }
}

class MemberInforDatePickerWidget extends StatelessWidget {
  const MemberInforDatePickerWidget({
    super.key,
    this.title,
    this.isRequired,
    this.selectedDate,
    this.pickDate,
    this.disableSelect,
    this.icon,
    this.lableWidget,
  });

  final String? title;
  final bool? isRequired;
  final DateTime? selectedDate;
  final Function(DateTime?)? pickDate;
  final bool? disableSelect;
  final Widget? icon;
  final Widget? lableWidget;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        lableWidget ??
            LableWidget(
              title: title,
              isRequired: isRequired,
            ),
        lableWidget != null
            ? const SizedBox(height: 8)
            : const SizedBox.shrink(),
        InkWell(
          onTap: () async {
            if (disableSelect == true) {
              final DateTime? picked = await showDatePicker(
                  context: context,
                  initialDate: selectedDate ?? DateTime.now(),
                  firstDate: DateTime(1700),
                  lastDate: DateTime.now());
              pickDate?.call(picked);
            }
          },
          child: Container(
            height: 48,
            padding: const EdgeInsets.symmetric(horizontal: 10),
            decoration: BoxDecoration(
              color: disableSelect == false ? Colors.red : Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  (selectedDate == null)
                      ? 'hint_pick_date'.tr
                      : DateFormat('dd/MM/yyyy').format(selectedDate!),
                  style: AppTextStyle.s14Medium.copyWith(
                    color: lableWidget != null
                        ? AppColors.black2E2E2E
                        : (selectedDate == null
                            ? AppColors.grey737373
                            : AppColors.black2E2E2E),
                  ),
                ),
                icon ??
                    SvgPicture.asset(
                      SvgPath.svgPickCalendart,
                      width: 24,
                      height: 24,
                    ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 15)
      ],
    );
  }
}
