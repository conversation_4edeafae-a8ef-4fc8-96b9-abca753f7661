import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/constants/enum_filter_sim.dart';
import 'package:shopping/pages/package/sim_phone_number_controller.dart';
import 'package:shopping/pages/package/widgets/filter_endraw_widget.dart';
import 'package:shopping/widgets/multi_button.dart';

class FilterSimEndrawWidget extends StatelessWidget {
  const FilterSimEndrawWidget({super.key, required this.controller});
  final SimPhoneNumberController controller;
  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: AppColors.white,
      width: (MediaQuery.of(context).size.width) * 0.9,
      child: SafeArea(
          child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            GestureDetector(
              onTap: () {
                Navigator.of(context).pop();
              },
              child: Align(
                  alignment: Alignment.centerRight,
                  child: SvgPicture.asset(SvgPath.svgIconClose)),
            ),
            const SizedBox(height: 10),
            Row(
              children: [
                SvgPicture.asset(
                  SvgPath.svgIconFilter,
                  height: 24,
                  width: 24,
                  color: AppColors.redFF6540,
                ),
                const SizedBox(width: 13),
                Text("Bộ lọc", style: AppTextStyle.s18Bold)
              ],
            ),
            const SizedBox(height: 19),
            Expanded(
                child: SingleChildScrollView(
              physics: ClampingScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("Nhà mạng", style: AppTextStyle.s14Bold),
                  const SizedBox(height: 20),
                  GetBuilder<SimPhoneNumberController>(
                      id: SimPhoneNumberControllerUpdateKey
                          .updateListSupplierType,
                      builder: (controller) => ListView.builder(
                            physics: NeverScrollableScrollPhysics(),
                            shrinkWrap: true,
                            itemCount: SupplierType.values.length,
                            itemBuilder: (context, index) {
                              final SupplierType supplierType =
                                  SupplierType.values[index];
                              return GestureDetector(
                                child: CheckBoxLineWidget(
                                    selectedColorLable: AppColors.blue00A1E4,
                                    title: supplierType.getName,
                                    isSelected: controller
                                        .isFilterSupplierSelected(supplierType),
                                    onChanged: (p0) {}),
                              );
                            },
                          )),
                  const SizedBox(height: 20),
                  Text("Đầu số", style: AppTextStyle.s14Bold),
                  const SizedBox(height: 20),
                  GetBuilder<SimPhoneNumberController>(
                      id: SimPhoneNumberControllerUpdateKey
                          .updateListPhonenumberStartAtType,
                      builder: (controller) => GridView.builder(
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          itemCount: PhonenumberStartAt.values.length,
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 2, childAspectRatio: 81 / 28),
                          itemBuilder: (context, index) {
                            final PhonenumberStartAt phonenumberStartAt =
                                PhonenumberStartAt.values[index];
                            return GestureDetector(
                              onTap: () {
                                controller.onSelectFilterPhonenumberStartAt(
                                    phonenumberStartAt);
                              },
                              child: RadioBtnPrefixLineWidget(
                                item: phonenumberStartAt,
                                groupValue:
                                    controller.phonenumberStartAtChoosed,
                                onChanged: (p0) {
                                  controller.onSelectFilterPhonenumberStartAt(
                                      phonenumberStartAt);
                                },
                              ),
                            );
                          }))
                ],
              ),
            )),
            const SizedBox(height: 8),
            MultiButton(
              textLeft: 'THIẾT LẬP LẠI',
              textRight: 'TIẾP TỤC',
              onTapLeft: () async {
                await controller.onResetFilter();
              },
              onTapRight: () async {
                controller.onSubmitFilter();
                Navigator.pop(context);
              },
            ),
            const SizedBox(height: 25)
          ],
        ),
      )),
    );
  }
}

class RadioBtnPrefixLineWidget extends StatelessWidget {
  const RadioBtnPrefixLineWidget({
    super.key,
    required this.item,
    required this.groupValue,
    required this.onChanged,
  });
  final PhonenumberStartAt item;
  final PhonenumberStartAt? groupValue;
  final Function(PhonenumberStartAt?) onChanged;
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SizedBox(
          width: 20,
          height: 32,
          child: Radio<PhonenumberStartAt>(
            toggleable: true,
            activeColor: AppColors.redCE3722,
            fillColor: WidgetStateProperty.resolveWith<Color>(
                (Set<WidgetState> states) {
              if (states.contains(WidgetState.selected)) {
                return AppColors.redCE3722;
              }
              return AppColors.greyDFE4EA;
            }),
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            value: item,
            groupValue: groupValue,
            onChanged: onChanged,
          ),
        ),
        const SizedBox(width: 10),
        Text(
          item.getName,
          style: AppTextStyle.s14Medium.copyWith(
            fontWeight: FontWeight.w500,
            height: 24 / 14,
            color: item == groupValue
                ? AppColors.redCE3722
                : AppColors.black111928,
          ),
        ),
      ],
    );
  }
}
