import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/constants/enum_filter_product.dart';
import 'package:shopping/pages/package/package_controller.dart';
import 'package:shopping/widgets/multi_button.dart';

class FilterCondition {
  static List<CycleType> listCycleType = [
    CycleType.day,
    CycleType.week,
    CycleType.month
  ];
  static List<PriceFromType> listPriceFromType = [
    PriceFromType.f0T100,
    PriceFromType.f100T300,
    PriceFromType.f300T600,
    PriceFromType.f600T1M,
    PriceFromType.f1M,
  ];
  static List<OrderByType> listOrderByType = [
    OrderByType.increase,
    OrderByType.reduce
  ];
}

class FilterEndrawWidget extends StatelessWidget {
  const FilterEndrawWidget({super.key, required this.controller});
  final PackageController controller;
  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: AppColors.white,
      width: (MediaQuery.of(context).size.width) * 0.9,
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: () {
                  Navigator.of(context).pop();
                },
                child: Align(
                    alignment: Alignment.centerRight,
                    child: SvgPicture.asset(SvgPath.svgIconClose)),
              ),
              const SizedBox(height: 10),
              Row(
                children: [
                  SvgPicture.asset(
                    SvgPath.svgIconFilter,
                    height: 24,
                    width: 24,
                    color: AppColors.redFF6540,
                  ),
                  const SizedBox(width: 13),
                  Text("Bộ lọc", style: AppTextStyle.s18Bold)
                ],
              ),
              const SizedBox(height: 17),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Chu kỳ gói cước",
                        style: AppTextStyle.s16SemiBold,
                      ),
                      const SizedBox(height: 5),
                      GetBuilder<PackageController>(
                        id: PackageControllerUpdateKey.updateListCycleType,
                        builder: (_) => ListView.builder(
                          physics: NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          itemCount: FilterCondition.listCycleType.length,
                          itemBuilder: (context, index) {
                            return GestureDetector(
                              onTap: () => controller.onSelectFilterCycle(
                                  FilterCondition.listCycleType[index]),
                              child: CheckBoxLineWidget(
                                title: FilterCondition
                                    .listCycleType[index].nameCycleType,
                                isSelected: controller.isFilterCycleSelected(
                                    FilterCondition.listCycleType[index]),
                                onChanged: (p0) =>
                                    controller.onSelectFilterCycle(
                                        FilterCondition.listCycleType[index]),
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        "Khoảng giá",
                        style: AppTextStyle.s16SemiBold,
                      ),
                      const SizedBox(height: 5),
                      GetBuilder<PackageController>(
                        id: PackageControllerUpdateKey.updateListPriceType,
                        builder: (_) => ListView.builder(
                          physics: NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          itemCount: FilterCondition.listPriceFromType.length,
                          itemBuilder: (context, index) {
                            return GestureDetector(
                              onTap: () => controller.onSelectFilterPrice(
                                  FilterCondition.listPriceFromType[index]),
                              child: CheckBoxLineWidget(
                                title: FilterCondition
                                    .listPriceFromType[index].namePriceFrom,
                                isSelected: controller.isFilterPriceSelected(
                                    FilterCondition.listPriceFromType[index]),
                                onChanged: (p0) => controller
                                    .onSelectFilterPrice(FilterCondition
                                        .listPriceFromType[index]),
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        "Sắp xếp",
                        style: AppTextStyle.s16SemiBold,
                      ),
                      GetBuilder<PackageController>(
                          id: PackageControllerUpdateKey.updateOrderByType,
                          builder: (_) => ListView.builder(
                                physics: NeverScrollableScrollPhysics(),
                                shrinkWrap: true,
                                itemCount:
                                    FilterCondition.listOrderByType.length,
                                itemBuilder: (context, index) {
                                  return GestureDetector(
                                    onTap: () => controller
                                        .onSelectFilterOrderBy(FilterCondition
                                            .listOrderByType[index]),
                                    child: RadioBtnLineWidget(
                                      item: FilterCondition
                                          .listOrderByType[index],
                                      groupValue: controller.orderByTypeChoosed,
                                      onChanged: (p0) => controller
                                          .onSelectFilterOrderBy(FilterCondition
                                              .listOrderByType[index]),
                                    ),
                                  );
                                },
                              ))
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 8),
              MultiButton(
                textLeft: 'THIẾT LẬP LẠI',
                textRight: 'TIẾP TỤC',
                onTapLeft: controller.onResetFilter,
                onTapRight: () {
                  controller.onSubmitFilter();
                  Navigator.pop(context);
                },
              ),
              const SizedBox(height: 25)
            ],
          ),
        ),
      ),
    );
  }
}

class CheckBoxLineWidget extends StatelessWidget {
  const CheckBoxLineWidget(
      {super.key,
      this.title,
      required this.isSelected,
      required this.onChanged,
      this.selectedColorLable,
      this.content});
  final String? title;
  final Widget? content;
  final bool isSelected;
  final Function(bool?) onChanged;
  final Color? selectedColorLable;
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 20,
          height: 32,
          child: Checkbox(
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            activeColor: AppColors.redCE3722,
            value: isSelected,
            onChanged: onChanged,
            side: const BorderSide(width: 1, color: AppColors.greyDFE4EA),
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(5.0),
              ),
            ),
          ),
        ),
        const SizedBox(width: 10),
        Flexible(
          child: Padding(
            padding: const EdgeInsets.only(top: 4),
            child: content != null
                ? content
                : Text(
                    title ?? 'N/A',
                    style: AppTextStyle.s14Medium.copyWith(
                      fontWeight: FontWeight.w500,
                      color: isSelected
                          ? selectedColorLable ?? AppColors.black111928
                          : AppColors.black111928,
                    ),
                  ),
          ),
        ),
      ],
    );
  }
}

class RadioBtnLineWidget extends StatelessWidget {
  const RadioBtnLineWidget({
    super.key,
    required this.item,
    required this.groupValue,
    required this.onChanged,
  });
  final OrderByType item;
  final OrderByType? groupValue;
  final Function(OrderByType?) onChanged;
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SizedBox(
          width: 20,
          height: 32,
          child: Radio<OrderByType>(
            toggleable: true,
            activeColor: AppColors.redCE3722,
            fillColor: WidgetStateProperty.resolveWith<Color>(
                (Set<WidgetState> states) {
              if (states.contains(WidgetState.selected)) {
                return AppColors.redCE3722;
              }
              return AppColors.greyDFE4EA;
            }),
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            value: item,
            groupValue: groupValue,
            onChanged: onChanged,
          ),
        ),
        const SizedBox(width: 10),
        Text(
          item.nameFilter,
          style: AppTextStyle.s14Medium.copyWith(
            fontWeight: FontWeight.w500,
            height: 24 / 14,
            color: AppColors.black111928,
          ),
        ),
      ],
    );
  }
}
