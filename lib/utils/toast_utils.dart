import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../constants/app_colors.dart';

class ToastUtils {
  static showSuccess(String? message) {
    return Fluttertoast.showToast(
      msg: message ?? '',
      backgroundColor: AppColors.blue005BF9.withOpacity(0.5),
      textColor: Colors.white,
    );
  }

  static showFail(String? message, {ToastGravity? gravity, double? opacity}) {
    return Fluttertoast.showToast(
      msg: message ?? '',
      toastLength: Toast.LENGTH_SHORT,
      gravity: gravity ?? ToastGravity.CENTER,
      backgroundColor: AppColors.redCE3722,
      textColor: Colors.white,
    );
  }
}
