import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:get/get.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/models/product.dart';
import 'package:shopping/pages/sim/item_sim_action.dart';
import 'package:shopping/pages/sim/sim_order_screen.dart';
import 'package:shopping/pages/sim/step_pick_product_controller.dart';
import 'package:shopping/tabs/home/<USER>';
import 'package:shopping/tabs/home/<USER>/pick_payment_type_widget.dart';
import 'package:shopping/tabs/home/<USER>/timer_count.dart';
import 'package:shopping/utils/convert/time_util.dart';
import 'package:shopping/utils/extension/int_extension.dart';
import 'package:shopping/widgets/app_text_field_number.dart';

import '../../../widgets/app_dialog.dart';

class SecondPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Text("Hello"),
    );
  }
}

class ItemPackageAction {
  // để sau truyền thêm id package
  static openDetail(BuildContext context, Product product,
      {bool? isBuyWithSim,
      int? ordId,
      String? phoneNumber,
      String? userPhoneNumber}) {
    print("ItemPackageAction openDetail");
    bool _goNextSheet = false;
    // showCupertinoModalBottomSheet(
    //   context: context,
    //   expand: true,
    //   // useRootNavigator: true,
    //   bounce: true,
    //   enableDrag: true,
    //   barrierColor: Color.fromRGBO(0, 0, 0, 0.75),
    //   isDismissible: true,
    //   closeProgressThreshold: 0.5,
    //   builder: (context) => build_modal_package_detail(
    //       openBuy: () {
    //         // close detail and open sheet buy
    //         Navigator.pop(context);
    //         _goNextSheet = true;
    //       },
    //       product: product),
    // ).whenComplete(() {
    //   if (_goNextSheet) openSheetBuy(context, product);
    // });

    showModalBottomSheet(
        context: context,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
        isScrollControlled: true,
        builder: (_) => Container(
            padding: EdgeInsets.symmetric(horizontal: 8),
            decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(30)),
            width: double.infinity,
            child: Stack(
              children: [
                Container(
                  margin: EdgeInsets.only(bottom: 100),
                  child: DraggableScrollableSheet(
                    expand: false,
                    // snap: true,
                    // snapSizes: [0.68, 0.7, 0.9],
                    initialChildSize: 0.7,
                    minChildSize: 0.68,
                    maxChildSize: 0.9,
                    builder: (context, scrollController) =>
                        SingleChildScrollView(
                      // controller: scrollController,
                      physics: NeverScrollableScrollPhysics(),
                      child: _PackageDetailView(
                        openBuy: () {
                          // close detail and open sheet buy
                          Navigator.pop(context);
                          _goNextSheet = true;
                        },
                        product: product,
                        scrollController: scrollController,
                      ),
                    ),
                  ),
                ),
                Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: GestureDetector(
                      onTap: () {
                        if ((isBuyWithSim ?? false)) {
                          var _controller =
                              Get.find<StepPickProductController>();
                          if (_controller.countTime.value <= 0) {
                            dialogAsk(
                              context,
                              'Đã hết thời gian giữ số. Vui lòng thực hiện lại.',
                              textButton: 'ĐÓNG',
                              isBackAction: true,
                              callOk: () {
                                // close popup
                                Navigator.of(context).pop();
                                // close dialog
                                Navigator.of(context).pop();
                                // pop về list SIM số
                                ActionBack.onBack(context: context);
                                // Navigator.of(context).popUntil(
                                //     ModalRoute.withName("/page_list_sim"));
                              },
                            );
                          } else {
                            Navigator.pop(context);
                            _goNextSheet = true;
                          }
                        } else {
                          Navigator.pop(context);
                          _goNextSheet = true;
                        }
                      },
                      child: Container(
                        width: MediaQuery.of(context).size.width - 60,
                        height: 100,
                        color: AppColors.white,
                        margin: EdgeInsets.symmetric(horizontal: 15),
                        child: Center(
                          child: Container(
                            width: double.infinity,
                            height: 45,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                color: AppColors.blue30AAB7),
                            child: Center(
                                child: Text(
                              (isBuyWithSim ?? false)
                                  ? 'CHỌN GÓI'
                                  : "ĐĂNG KÝ NGAY",
                              style: AppTextStyle.s16Medium
                                  .copyWith(color: AppColors.white),
                            )),
                          ),
                        ),
                      ),
                    ))
              ],
            ))).whenComplete(() {
      if (_goNextSheet) {
        if (isBuyWithSim ?? false) {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => SIMOrderScreen(
                        ordId: ordId,
                        product: product,
                        phoneNumber: phoneNumber,
                        userPhoneNumber: userPhoneNumber,
                      )));
        } else {
          openSheetBuy(context, product);
        }
      }
    });
  }

  static build_modal_package_detail(
      {required Function() openBuy, required Product product}) {
    return Scaffold(
        backgroundColor: AppColors.white,
        body: SafeArea(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 15,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 45,
                    height: 5,
                    decoration: BoxDecoration(
                        color: AppColors.greyE5E6EC,
                        borderRadius: BorderRadius.circular(10)),
                  )
                ],
              ),
              SizedBox(
                height: 15,
              ),
              Container(
                margin: EdgeInsets.only(left: 15, right: 15),
                child: Text(
                  "${product.name}",
                  style: AppTextStyle.s20Bold,
                  maxLines: 2,
                  softWrap: true,
                ),
              ),
              Container(
                  margin: EdgeInsets.only(left: 15, right: 15),
                  child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          // "${product.price.convertToVietnamesMoney()}",
                          ConvertMoney.convertVNDMoney(product.price),
                          style: AppTextStyle.s24SemiBold
                              .copyWith(color: AppColors.redCE3722),
                        ),
                        Text(
                          'đ',
                          style: AppTextStyle.s13Bold.copyWith(
                              color: AppColors.blue005BF9,
                              decoration: TextDecoration.underline),
                          overflow: TextOverflow.ellipsis,
                        )
                      ])),
              SizedBox(
                height: 15,
              ),
              SizedBox(
                height: 40,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  shrinkWrap: true,
                  itemCount: 1,
                  itemBuilder: (context, index) {
                    return Container(
                      padding: EdgeInsets.all(8),
                      margin: EdgeInsets.only(left: 8),
                      decoration: BoxDecoration(
                          border:
                              Border.all(width: 1, color: AppColors.blue005BF9),
                          borderRadius: BorderRadius.circular(30)),
                      child: Row(
                        children: [
                          SvgPicture.asset(
                            SvgPath.svgIconCapacity,
                            color: AppColors.blue005BF9,
                          ),
                          const SizedBox(width: 8),
                          Text("${product.getChuKy()}",
                              style: AppTextStyle.s13Medium
                                  .copyWith(color: AppColors.blue005BF9))
                        ],
                      ),
                    );
                  },
                ),
              ),
              SizedBox(
                height: 20,
              ),
              Expanded(
                  child: SingleChildScrollView(
                child: Container(
                    color: Colors.white,
                    margin: EdgeInsets.only(left: 15, right: 15),
                    height: 300,
                    child: HtmlWidget(
                      product.description!,
                      customStylesBuilder: (element) {
                        switch (element.localName) {
                          case 'table':
                            return {
                              'border': '1px solid',
                              'border-collapse': 'collapse',
                            };
                          case 'td':
                            return {'border': '1px solid'};
                        }

                        return null;
                      },
                    )),
              )),
              SizedBox(
                height: 15,
              ),
              GestureDetector(
                onTap: openBuy,
                child: Container(
                  margin: EdgeInsets.symmetric(horizontal: 15),
                  height: 64,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(100),
                      color: AppColors.blue005BF9),
                  child: Center(
                      child: Text(
                    "ĐĂNG KÝ NGAY",
                    style:
                        AppTextStyle.s18Medium.copyWith(color: AppColors.white),
                  )),
                ),
              ),
              SizedBox(
                height: 15,
              ),
            ],
          ),
          top: true,
        ));
  }

  static void openSheetBuy(BuildContext context, Product product) {
    showModalBottomSheet(
        backgroundColor: AppColors.white,
        context: context,
        enableDrag: true,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
        isScrollControlled: true,
        builder: (context) => Container(
            padding: MediaQuery.of(context).viewInsets,
            width: double.infinity,
            child: SingleChildScrollView(
              child: GetBuilder<BuyPackageController>(
                  init: BuyPackageController(),
                  tag: "_BuyPackageWidget",
                  builder: (controller) => _BuyPackageWidget(
                        openPickPayment: ({required BuildContext ctx}
                            // {required Function() funcPayOnline,
                            // required Function() funcPayTKC}
                            ) {
                          Navigator.pop(context);
                          openSheetPickPaymentType(
                            context,
                            product: product,
                            phoneNumber: controller.phoneController.text.trim(),
                            referCode:
                                controller.referralCodeController.text.trim(),
                            openOTP: (int? ordId) {
                              Navigator.pop(ctx);
                              print('ORD ID = $ordId');
                              openSheetOTP(ctx, ordId ?? 0).then((value) {});
                            },
                            // funcPayOnline: funcPayOnline, funcPayTKC: funcPayTKC
                          );
                        },
                        openOTP: (int? ordId) {
                          Navigator.pop(context);
                          openSheetOTP(context, ordId ?? 0).then((value) {});
                        },
                        product: product,
                      )),
            )));
  }

  static Future<void> openSheetOTP(BuildContext context, int ordId) async {
    return await showModalBottomSheet(
        context: context,
        backgroundColor: AppColors.white,
        enableDrag: true,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
        isScrollControlled: true,
        builder: (context) => Container(
            padding: MediaQuery.of(context).viewInsets,
            width: double.infinity,
            child: _OTPBuyPackageWidget(
              ordId: ordId,
              openSuccessView: (String? orderCode, String? message) {
                // close
                Navigator.pop(context);
                // open sheet buy
                openSheetBuySuccess(context, orderCode ?? "", message ?? "");
              },
              openFaildView: (String? orderCode, String? message) {
                // close
                Navigator.pop(context);
                // open sheet buy
                openSheetBuyFaild(context, orderCode ?? "", message ?? "");
              },
            )));
  }

  static void openSheetPickPaymentType(BuildContext context,
      {required Product product,
      required Function(int?) openOTP,
      required String phoneNumber,
      required String referCode
      //   required Function() funcPayOnline,
      //  required Function() funcPayTKC,
      }) {
    showModalBottomSheet(
        backgroundColor: AppColors.white,
        context: context,
        enableDrag: true,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
        isScrollControlled: true,
        builder: (context) => Container(
            padding: MediaQuery.of(context).viewInsets,
            width: double.infinity,
            child: SingleChildScrollView(
              child: PickPaymentTypeWidget(
                product: product,
                openOTP: (int? ordId) {
                  openOTP.call(ordId);
                },
                phoneNumber: phoneNumber,
                referCode: referCode,
                // funcPayOnline: () {
                //   funcPayOnline.call();
                // },
                // funcPayTKC: () {
                //   funcPayTKC.call();
                // },
              ),
            )));
  }

  static void openSheetBuySuccess(
      BuildContext context, String orderCode, String messageValidate) {
    showModalBottomSheet(
        context: context,
        elevation: 0,
        backgroundColor: AppColors.white,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
        isScrollControlled: true,
        enableDrag: true,
        builder: (_) => Container(
            width: double.infinity,
            child: SingleChildScrollView(
              child: _BuySuccessView(
                orderCode: orderCode,
                messageValidate: messageValidate,
              ),
            )));
  }

  static void openSheetBuyFaild(
      BuildContext context, String orderCode, String messageValidate) {
    showModalBottomSheet(
        context: context,
        backgroundColor: AppColors.white,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
        isScrollControlled: true,
        enableDrag: true,
        builder: (_) => Container(
            width: double.infinity,
            child: SingleChildScrollView(
              child: _BuyFaildView(
                orderCode: orderCode,
                messageValidate: messageValidate,
              ),
            )));
  }
}

class _BuySuccessView extends StatelessWidget {
  final String orderCode;
  final String messageValidate;

  const _BuySuccessView(
      {required this.orderCode, required this.messageValidate});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<BuyPackageController>(
        init: BuyPackageController(),
        tag: "_OTPBuyPackageWidget",
        builder: (controller) => Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 11),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      height: 22,
                      child: Stack(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(bottom: 3),
                            child: Align(
                              alignment: Alignment.center,
                              child: Container(
                                width: 45,
                                height: 5,
                                decoration: BoxDecoration(
                                    color: AppColors.greyE5E6EC,
                                    borderRadius: BorderRadius.circular(10)),
                              ),
                            ),
                          ),
                          Align(
                            alignment: Alignment.centerRight,
                            child: GestureDetector(
                              onTap: () {
                                // close
                                Navigator.of(context).pop();
                              },
                              child: SvgPicture.asset(SvgPath.svgIconClose),
                            ),
                          )
                        ],
                      ),
                    ),
                    Container(
                      width: double.infinity,
                      height: 230,
                      child: Stack(children: [
                        SvgPicture.asset(
                          SvgPath.svgBgSuccess,
                          fit: BoxFit.fill,
                        ),
                        Positioned(
                          child: Padding(
                            padding: const EdgeInsets.only(top: 54),
                            child: Align(
                                alignment: Alignment.center,
                                child: Container(
                                  child: SvgPicture.asset(
                                    SvgPath.svgSuccess,
                                    fit: BoxFit.fitHeight,
                                  ),
                                )),
                          ),
                        )
                      ]),
                    ),
                    Center(
                        child: Text(
                      "ĐĂNG KÝ THÀNH CÔNG",
                      style: AppTextStyle.s18Bold,
                    )),
                    const SizedBox(height: 14),
                    Center(
                      child: RichText(
                        text: TextSpan(
                            text: 'Mã đơn hàng: ',
                            style: AppTextStyle.s13Regular.copyWith(
                                height: 24 / 14, color: AppColors.black2E2E2E),
                            children: <TextSpan>[
                              TextSpan(
                                  text: orderCode,
                                  style: AppTextStyle.s13Bold.copyWith(
                                      height: 24 / 14,
                                      color: AppColors.redCE3722,
                                      fontWeight: FontWeight.bold))
                            ]),
                      ),
                    ),
                    SizedBox(
                      height: 14,
                    ),
                    Center(
                        child: Text(
                      messageValidate,
                      style: AppTextStyle.s13Regular.copyWith(height: 20 / 14),
                      textAlign: TextAlign.center,
                    )),
                    const SizedBox(height: 21),
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Container(
                        padding: EdgeInsets.all(12),
                        width: double.infinity,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            color: AppColors.greyCACACA),
                        child: Center(
                            child: Text(
                          "QUAY LẠI",
                          style: AppTextStyle.s16Medium
                              .copyWith(color: AppColors.black2E2E2E),
                        )),
                      ),
                    ),
                    SizedBox(height: 15),
                  ]),
            ));
  }
}

class _BuyFaildView extends StatelessWidget {
  final String orderCode;
  final String messageValidate;
  const _BuyFaildView(
      {required this.orderCode, required this.messageValidate});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 11),
      child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: 22,
              child: Stack(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(bottom: 3),
                    child: Align(
                      alignment: Alignment.center,
                      child: Container(
                        width: 45,
                        height: 5,
                        decoration: BoxDecoration(
                            color: AppColors.greyE5E6EC,
                            borderRadius: BorderRadius.circular(10)),
                      ),
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerRight,
                    child: GestureDetector(
                      onTap: () {
                        // close
                        Navigator.of(context).pop();
                      },
                      child: SvgPicture.asset(SvgPath.svgIconClose),
                    ),
                  )
                ],
              ),
            ),
            Container(
              padding: EdgeInsets.only(top: 48, left: 109, right: 109),
              child: Center(
                child: SvgPicture.asset(SvgPath.svgFaild),
              ),
            ),
            Center(
                child: Text(
              "ĐĂNG KÝ KHÔNG THÀNH CÔNG",
              style: AppTextStyle.s18Bold,
              textAlign: TextAlign.center,
            )),
            SizedBox(
              height: 14,
            ),
            Center(
              child: RichText(
                text: TextSpan(
                    text: 'Mã đơn hàng: ',
                    style: AppTextStyle.s13Regular.copyWith(
                        height: 24 / 14, color: AppColors.black2E2E2E),
                    children: <TextSpan>[
                      TextSpan(
                          text: orderCode,
                          style: AppTextStyle.s13Bold.copyWith(
                              height: 24 / 14,
                              color: AppColors.redCE3722,
                              fontWeight: FontWeight.bold))
                    ]),
              ),
            ),
            SizedBox(
              height: 14,
            ),
            Center(
                child: Text(
              messageValidate,
              style: AppTextStyle.s13Regular
                  .copyWith(height: 24 / 14, color: AppColors.black2E2E2E),
              textAlign: TextAlign.center,
            )),
            const SizedBox(height: 21),
            GestureDetector(
              onTap: () {
                Navigator.pop(context);
              },
              child: Container(
                width: double.infinity,
                height: 64,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: AppColors.greyCACACA),
                child: Center(
                    child: Text(
                  "QUAY LẠI",
                  style: AppTextStyle.s16Medium
                      .copyWith(color: AppColors.black2E2E2E),
                )),
              ),
            ),
            SizedBox(height: 27),
          ]),
    );
  }
}

class LineBuyPackageWidget extends StatelessWidget {
  const LineBuyPackageWidget(
      {super.key, required this.name, required this.detail});
  final String name;
  final String detail;
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          "${name}: ",
          style: AppTextStyle.s15Regular.copyWith(height: 20 / 14),
        ),
        Flexible(
          child: Text(detail ?? '',
              style:
                  AppTextStyle.s15SemiBold.copyWith(color: AppColors.redCE3722),
              overflow: TextOverflow.ellipsis),
        )
      ],
    );
  }
}

class _BuyPackageWidget extends StatelessWidget {
  const _BuyPackageWidget(
      {required this.openOTP,
      required this.product,
      required this.openPickPayment});
  final Function(int?) openOTP;
  final Function({required BuildContext ctx}
      // {required Function() funcPayOnline,
      // required Function() funcPayTKC}
      ) openPickPayment;
  final Product? product;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<BuyPackageController>(
      init: BuyPackageController(),
      tag: "_BuyPackageWidget",
      builder: (controller) => Padding(
          padding: const EdgeInsets.only(left: 15, right: 15, top: 6),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                height: 22,
                child: Stack(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(bottom: 3),
                      child: Align(
                        alignment: Alignment.center,
                        child: Container(
                          width: 45,
                          height: 5,
                          decoration: BoxDecoration(
                              color: AppColors.greyE5E6EC,
                              borderRadius: BorderRadius.circular(10)),
                        ),
                      ),
                    ),
                    Align(
                      alignment: Alignment.centerRight,
                      child: GestureDetector(
                        onTap: () {
                          // close
                          Navigator.of(context).pop();
                        },
                        child: SvgPicture.asset(SvgPath.svgIconClose),
                      ),
                    )
                  ],
                ),
              ),
              Text(
                "Đăng ký gói cước",
                textAlign: TextAlign.center,
                style: AppTextStyle.s18Bold,
              ),
              const SizedBox(height: 16),
              LineBuyPackageWidget(
                name: "Tên gói cước",
                detail: "${product!.name}",
              ),
              LineBuyPackageWidget(
                name: "Chu kỳ sử dụng",
                detail: "${product!.getChuKy()}",
              ),
              LineBuyPackageWidget(
                name: "Giá",
                // detail: "${product!.price.convertToVietnamesMoney()}đ",
                detail: "${ConvertMoney.convertVNDMoney(product?.price)}đ",
              ),
              const SizedBox(height: 20),
              CustomAppTextField(
                maxLength: 12,
                hintText: "Nhập số điện thoại",
                controller: controller.phoneController,
                focusNode: controller.phoneFocusNode,
              ),
              const SizedBox(height: 20),
              GestureDetector(
                  onTap: () async {
                    await controller.checkValidateBuyPackage(
                      context: context,
                      product: product!,
                      openOTP: (orderId) {
                        openOTP.call(orderId);
                      },
                      openSheetPickPaymentType: ({required BuildContext ctx}
                          // {required Function() funcPayOnline,
                          // required Function() funcPayTKC}
                          ) {
                        openPickPayment.call(ctx: ctx
                            //   funcPayOnline: () {
                            //   funcPayOnline.call();
                            // }, funcPayTKC: () {
                            //   funcPayTKC.call();
                            // }
                            );
                      },
                    );
                  },
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(12),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: AppColors.green30AAB7),
                    child: Obx(
                      () => !controller.isSendServer.value
                          ? Center(
                              child: Text(
                              "TIẾP TỤC",
                              style: AppTextStyle.s16Medium
                                  .copyWith(color: AppColors.white),
                            ))
                          : Center(
                              child: CircularProgressIndicator(
                                  color: AppColors.whiteFFFFFF)),
                    ),
                  )),
              SizedBox(height: 32),
            ],
          )),
    );
  }
}

class _OTPBuyPackageWidget extends StatelessWidget {
  const _OTPBuyPackageWidget(
      {required this.openSuccessView,
      required this.openFaildView,
      required this.ordId});
  final Function(String?, String?) openSuccessView;
  final Function(String?, String?) openFaildView;
  final int ordId;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<BuyPackageController>(
        init: BuyPackageController(),
        tag: "_OTPBuyPackageWidget",
        builder: (controller) => LoaderOverlay(
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 15, vertical: 7),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      height: 22,
                      child: Stack(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(bottom: 3),
                            child: Align(
                              alignment: Alignment.center,
                              child: Container(
                                width: 45,
                                height: 5,
                                decoration: BoxDecoration(
                                    color: AppColors.greyE5E6EC,
                                    borderRadius: BorderRadius.circular(10)),
                              ),
                            ),
                          ),
                          Align(
                            alignment: Alignment.centerRight,
                            child: GestureDetector(
                              onTap: () {
                                // close
                                Navigator.of(context).pop();
                              },
                              child: SvgPicture.asset(SvgPath.svgIconClose),
                            ),
                          )
                        ],
                      ),
                    ),

                    // Center(
                    //   child: Container(
                    //     width: 45,
                    //     height: 5,
                    //     decoration: BoxDecoration(color: AppColors.greyE5E6EC, borderRadius: BorderRadius.circular(10)),
                    //   ),
                    // ),
                    // GestureDetector(
                    //   onTap: () {
                    //     // close
                    //     Navigator.of(context).pop();
                    //   },
                    //   child: Align(alignment: Alignment.centerRight, child: SvgPicture.asset(SvgPath.svgIconClose)),
                    // ),

                    Text(
                      "Nhập mã xác thực",
                      style: AppTextStyle.s18Bold,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 22),
                    Text(
                      "Vui lòng nhập mã xác thực đã được gửi đến số điện thoại liên hệ",
                      style: AppTextStyle.s13Regular.copyWith(height: 20 / 14),
                    ),
                    const SizedBox(height: 24),
                    CustomAppTextField(
                      hintText: "Nhập mã xác nhận",
                      maxLength: 20,
                      controller: controller.otpController,
                    ),
                    const SizedBox(height: 10),

                    Obx(
                      () => TimerCount(
                        resentOtp: () {
                          print('set resend otp');
                          controller.setResend(true);
                          controller.resendOTP(context, ordId);
                        },
                        rs: controller.rs.value,
                        isCountDown: controller.isCountDown.value,
                      ),
                    ),
                    // GestureDetector(
                    //     onTap: () {
                    //       // gửi lại otp
                    //       if (controller.disableResend.value!) {
                    //         print("resend otp");
                    //         controller.sendOTP();
                    //         controller.resendOTP(context, controller.orderId);
                    //       }
                    //     },
                    //     child: Obx(
                    //       () => Align(
                    //         alignment: Alignment.center,
                    //         child: RichText(
                    //           text: TextSpan(text: 'Không nhận được mã OTP? ', style: AppTextStyle.s14Regular.copyWith(height: 20 / 14, color: AppColors.black2E2E2E), children: <TextSpan>[
                    //             TextSpan(
                    //                 text: 'Gửi lại', style: AppTextStyle.s14Regular.copyWith(height: 20 / 14, color: (controller.disableResend.value!) ? AppColors.blue005BF9 : AppColors.grey6A6A6A))
                    //           ]),
                    //         ),
                    //       ),
                    //     )),
                    const SizedBox(height: 21),
                    GestureDetector(
                      onTap: () async {
                        await controller.checkValidateOtp(context, ordId,
                            (otpcode, message) {
                          openSuccessView.call(otpcode, message);
                        }, (otpcode, message) {
                          openFaildView.call(otpcode, message);
                        });
                      },
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(12),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            color: AppColors.green30AAB7),
                        child: GetBuilder<BuyPackageController>(
                            id: 'buy_package_loading',
                            tag: "_OTPBuyPackageWidget",
                            builder: (controller) => Obx(() =>
                                !controller.isSendServer.value
                                    ? Center(
                                        child: Text(
                                        "TIẾP TỤC",
                                        style: AppTextStyle.s16Medium
                                            .copyWith(color: AppColors.white),
                                      ))
                                    : Center(
                                        child: CircularProgressIndicator(
                                            color: AppColors.whiteFFFFFF)))),
                      ),
                    ),
                    const SizedBox(height: 31),
                  ],
                ),
              ),
            ));
  }

  timeOtp(BuyPackageController controller) {
    return Obx(() => Center(
          child: Visibility(
            visible: controller.disableBtnResend.value!,
            child: RichText(
              text: TextSpan(
                  text: 'Thời gian còn hiệu lực:',
                  style: AppTextStyle.s15Medium.copyWith(
                    color: AppColors.black2E2E2E,
                  ),
                  children: <TextSpan>[
                    TextSpan(
                        text:
                            TimeUtil.formatTime(time: controller.timeOtp.value),
                        style: AppTextStyle.s15Bold.copyWith(
                          color: AppColors.black,
                        )),
                  ]),
            ),
          ),
        ));
  }
}

// class BuyPackageController extends GetxController {
//   final phoneController = TextEditingController();
//   final referralCodeController = TextEditingController();
//   final otpController = TextEditingController();

//   //send server
//   var isSendServer = false.obs;

//   final phoneFocusNode = FocusNode();
//   var focusPhone = Rxn(false);
//   int orderId = 0;
//   String messageValidate = "";
//   String orderCode = "";

//   Timer? timer, timerDelay;

//   var timeOtp = 180.obs;
//   var delay = 1800.obs;

//   int countFailOtp = 0;
//   int maxFailOtp = 5;

//   int countOtp = 0;
//   int maxOtp = 5;
//   int timeStart = 0;

//   var enablePin = Rxn(true);
//   var disableTimeOtp = Rxn(true);
//   var disableResend = Rxn(true);
//   var disableBtnResend = Rxn(true);

//   var rs = true.obs;
//   var isCountDown = true.obs;

//   void startTimerOtp() {
//     timeOtp = 180.obs;
//     Timer.periodic(
//       const Duration(seconds: 1),
//       (Timer t) {
//         if (timer == null) {
//           timer = t;
//         }
//         if (timeOtp.value == 0) {
//           t.cancel();

//           disableResend.value = true;
//           disableTimeOtp.value = false;
//           enablePin.value = false;
//         } else {
//           timeOtp--;
//           print(timeOtp.value);
//         }
//       },
//     );
//     update();
//   }

//   bool isCountDownOtp() {
//     return timeOtp.value > 0;
//   }

//   void setResend(bool? r) {
//     rs.value = r ?? false;
//     print('set resend $r and ${rs.value}');
//   }

//   void setupLayout(BuildContext context) {
//     int timeNow = (DateTime.now().millisecondsSinceEpoch ~/ 1000);

//     if (AppStorage.instance.getInt2(SKeysPK.timeStart) != null) {
//       int timeValidity =
//           timeNow - AppStorage.instance.getInt2(SKeysPK.timeStart)!;
//       if (timeValidity <= 1800) {
//         enablePin.value = false;
//         disableTimeOtp.value = false;
//         disableResend.value = false;
//         startTimeDelay(1800 - timeValidity);
//         if (AppStorage.instance.getInt2(SKeysPK.countOtp)! == maxOtp) {
//           //  dialogAsk(context, "Vượt quá số lượng OTP. Vui lòng thử lại sau");
//           return;
//         }
//       } else {
//         sendOTP();
//       }
//     } else {
//       print("send OTP");
//       sendOTP();
//     }
//   }

//   void startTimeDelay(int time) {
//     delay = time.obs;
//     timerDelay = Timer.periodic(
//       const Duration(seconds: 1),
//       (Timer timer) {
//         if (delay.value == 0) {
//           timer.cancel();
//           // countOtp = AppStorage().getInt(SKeys.countOtp)!;
//           debugPrint('count otp in delay: $countOtp');
//           if (countOtp == maxOtp) {
//             AppStorage.instance.removeString2(SKeysPK.countOtp);
//             countOtp = 0;
//           }
//           //countFailOtp = 0;
//           //countResend = 0;
//           enablePin.value = true;
//           disableResend.value = true;
//           disableBtnResend.value = true;
//           disableTimeOtp.value = false;
//           AppStorage.instance.removeString2(SKeysPK.timeStart);
//           debugPrint('end time delay');
//         } else {
//           delay--;
//           // debugPrint('delay time: $delay');
//         }
//       },
//     );
//     update();
//   }

//   @override
//   void onInit() {
//     super.onInit();
//     timer?.cancel();
//     timerDelay?.cancel();
//     String strPhone = Get.isRegistered<PackageController>()
//         ? Get.find<PackageController>().searchKeyWord
//         : Get.isRegistered<SearchHomeController>()
//             ? Get.find<SearchHomeController>().keyword.value
//             : '';
//     phoneController.text =
//         (ValidatorUlti.isPhoneNumber(strPhone.trim())) ? strPhone.trim() : '';
//     print("BuyPackageController init");
//   }

//   @override
//   void onReady() {
//     super.onReady();
//     phoneController.addListener(() {});
//     referralCodeController.addListener(() {});
//     phoneFocusNode.addListener(() {
//       focusPhone.value = phoneFocusNode.hasFocus;
//     });
//   }

//   @override
//   void onClose() {
//     super.onClose();
//     timer?.cancel();
//     timerDelay?.cancel();
//     isSendServer.value = false;
//   }

//   void sendOTP() {
//     if (AppStorage.instance.getInt2(SKeysPK.countOtp) != null) {
//       countOtp = AppStorage.instance.getInt2(SKeysPK.countOtp)!;
//     }
//     disableResend.value = false;
//     if (countOtp != maxOtp) {
//       // startTimerOtp();
//       countOtp++;
//       print(countOtp);
//       AppStorage.instance.setInt2(SKeysPK.countOtp, countOtp);
//     } else {
//       // ToastUtils.showSuccess("Vượt quá số lượng OTP. Vui lòng thử lại sau");
//       disableTimeOtp.value = false;
//       disableBtnResend.value = false;
//       enablePin.value = false;
//       AppStorage.instance.setInt2(
//           SKeysPK.timeStart, DateTime.now().millisecondsSinceEpoch ~/ 1000);
//       print(DateTime.now().millisecondsSinceEpoch ~/ 1000);
//       startTimeDelay(1800);
//     }
//     update();
//   }

//   checkValidateBuyPackage(
//       BuildContext context, Product product, Function(int?) openOTP) {
//     if (phoneController.text.isEmpty) {
//       dialogAsk(context, 'Số điện thoại không được để trống.');
//       return;
//     }
//     if (phoneController.text.length < 9 || phoneController.text.length > 12) {
//       dialogAsk(context, 'Số điện thoại từ 9 đến 12 ký tự');
//       return;
//     }
//     String number = ValidatorUlti.normalizePhoneNumber(phoneController.text);
//     if (!ValidatorUlti.isPhoneNumber(number)) {
//       dialogAsk(context, 'Số điện thoại không đúng định dạng.');
//       return;
//     }
//     setupLayout(context);
//     buyPackage(context, phoneController.text, product.id!,
//         referralCodeController.text, 0, openOTP);
//   }

//   checkValidateOtp(
//       BuildContext context,
//       int ordId,
//       Function(String?, String?) openBuySuccess,
//       Function(String?, String?) openBuyFail) {
//     if (isSendServer.value == true) {
//       return;
//     }
//     if (otpController.text.isEmpty) {
//       dialogAsk(context, 'Mã xác nhận không được để trống');
//       return;
//     }
//     validateBuyPackage(
//         context, ordId, otpController.text, openBuySuccess, openBuyFail);
//     // update();
//   }

//   void buyPackage(BuildContext context, String phone, int productId,
//       String referCode, int platform, Function(int?) openOTP) async {
//     isSendServer.value = true;
//     var res = await ShoppingRepository.instance
//         .postOrderBuyPackage2(phone, productId, referCode, platform);
//     if (res.code == 0) {
//       isSendServer.value = false;
//       orderId = res.data!.orderId!;
//       sendOTP();
//       openOTP.call(orderId);
//     } else {
//       isSendServer.value = false;
//       dialogAsk(context, res.message);
//     }
//     update();
//   }

//   void validateBuyPackage(
//       BuildContext context,
//       int orderId,
//       String otp,
//       Function(String?, String?) openBuySuccess,
//       Function(String?, String?) openBuyFail) async {
//     isSendServer.value = true;
//     var res =
//         await ShoppingRepository.instance.postValidateBuyPackage2(orderId, otp);
//     //kiem tra xem connect server thanh cong hay chua
//     bool isConnectServerSuccess = false;
//     if (res.data != null) {
//       isConnectServerSuccess = true;
//       isSendServer.value = false;
//       orderCode = res.data!.orderCode!;
//       messageValidate = res.data!.messages ?? "";
//     } else {
//       isConnectServerSuccess = false;
//       isSendServer.value = false;
//       messageValidate = res.message ?? "";
//     }
//     if (res.code == 0) {
//       isSendServer.value = false;
//       if (res.data != null) {
//         openBuySuccess.call(orderCode, messageValidate);
//         Get.find<OrderController>().changePage(0);
//       } else {
//         dialogAsk(context, messageValidate);
//       }
//     } else {
//       isSendServer.value = false;
//       if (res.code == 20007) {
//         dialogAsk(context, res.message);
//       } else if (res.code == 20008) {
//         dialogAsk(context, res.message);
//       } else {
//         //neu khong connect server thanh cong thi thong bao loi he thong
//         if (!isConnectServerSuccess) {
//           dialogAsk(context, messageValidate);
//         } else {
//           openBuyFail.call(orderCode, messageValidate);
//           Get.find<OrderController>().changePage(0);
//         }
//       }
//     }
//     update(['buy_package_loading']);
//   }

//   void resendOTP(BuildContext context, int orderId) async {
//     var res = await ShoppingRepository.instance.postResendOtp2(orderId);
//     if (res.code == 0) {
//     } else {
//       if (res.code == 30006) {
//         // disableResend.value = false;
//         // disableBtnResend.value = false;
//         isCountDown.value = false;
//         dialogAsk(context, res.message, isExit: true);
//         return;
//       }
//       dialogAsk(context, res.message);
//     }
//     update();
//   }
// }

class _PackageDetailView extends StatelessWidget {
  const _PackageDetailView(
      {required this.openBuy,
      required this.product,
      required this.scrollController});
  final Function() openBuy;
  final Product product;
  final ScrollController scrollController;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 15),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 22,
            child: Stack(
              children: [
                Padding(
                  padding: const EdgeInsets.only(bottom: 3),
                  child: Align(
                    alignment: Alignment.center,
                    child: Container(
                      width: 45,
                      height: 5,
                      decoration: BoxDecoration(
                          color: AppColors.greyE5E6EC,
                          borderRadius: BorderRadius.circular(10)),
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.centerRight,
                  child: GestureDetector(
                    onTap: () {
                      // close
                      Navigator.of(context).pop();
                    },
                    child: SvgPicture.asset(SvgPath.svgIconClose),
                  ),
                )
              ],
            ),
          ),
          // SizedBox(
          //   height: 15,
          // ),
          // Row(
          //   mainAxisAlignment: MainAxisAlignment.center,
          //   children: [
          //     Container(
          //       width: 45,
          //       height: 5,
          //       decoration: BoxDecoration(
          //           color: AppColors.greyE5E6EC,
          //           borderRadius: BorderRadius.circular(10)),
          //     )
          //   ],
          // ),
          // SizedBox(
          //   height: 15,
          // ),
          Container(
            margin: EdgeInsets.only(left: 15, right: 15),
            child: Text(
              "${product.name}",
              style: AppTextStyle.s20Bold,
              maxLines: 2,
              softWrap: true,
            ),
          ),
          Container(
              margin: EdgeInsets.only(left: 15, right: 15),
              child:
                  Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
                Text(
                  "${ConvertMoney.convertVNDMoney(product.price)}",
                  style: AppTextStyle.s24SemiBold
                      .copyWith(color: AppColors.redCE3722),
                ),
                Text(
                  'đ',
                  style: AppTextStyle.s13Bold.copyWith(
                      color: AppColors.redCE3722,
                      decoration: TextDecoration.underline,
                      decorationColor: AppColors.redCE3722),
                  overflow: TextOverflow.ellipsis,
                )
              ])),
          SizedBox(
            height: 15,
          ),
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              shrinkWrap: true,
              itemCount: 1,
              itemBuilder: (context, index) {
                return Container(
                  padding: EdgeInsets.all(8),
                  margin: EdgeInsets.only(left: 8),
                  decoration: BoxDecoration(
                      border: Border.all(width: 1, color: AppColors.blue30AAB7),
                      borderRadius: BorderRadius.circular(30)),
                  child: Row(
                    children: [
                      SvgPicture.asset(
                        SvgPath.svgIconCapacity,
                        colorFilter: ColorFilter.mode(AppColors.blue30AAB7, BlendMode.srcIn),
                      ),
                      const SizedBox(width: 8),
                      Text("${product.getChuKy()}",
                          style: AppTextStyle.s13Medium
                              .copyWith(color: AppColors.blue30AAB7))
                    ],
                  ),
                );
              },
            ),
          ),
          SizedBox(
            height: 20,
          ),
          Container(
            height: (MediaQuery.of(context).size.height * 0.9) - 280,
            child: SingleChildScrollView(
              controller: scrollController,
              child: Container(
                  margin: EdgeInsets.only(left: 15, right: 15),
                  child: HtmlWidget(
                    product.description!,
                    customStylesBuilder: (element) {
                      switch (element.localName) {
                        case 'table':
                          return {
                            'border': '1px solid',
                            'border-collapse': 'collapse',
                          };
                        case 'td':
                          return {'border': '1px solid'};
                      }

                      return null;
                    },
                  )),
            ),
          ),
          // Container(
          //   height: 100,
          //   width: 100,
          //   color: Colors.amber,
          // ),
        ],
      ),
    );
  }
}
