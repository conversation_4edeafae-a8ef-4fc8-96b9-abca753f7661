import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';

class CustomTextField extends StatelessWidget {
  final TextEditingController? controller;
  FocusNode? focusNode;
  bool autoFocus;
  bool showPassword;
  final Color colorText;
  final Color colorHint;
  final double fontSize;
  bool autoCorrect;
  final String? hinText;
  final double radius;
  final Widget? suffixIcon;
  final TextInputType keyBoardType;
  final int? maxLenght;
  final int? maxLine;
  final TextInputAction? textInputAction;
  final InputBorder? inputBorder;
  final TextStyle? style;
  final EdgeInsetsGeometry? contentPadding;
  final Function(String)? onChanged;
  final bool? readOnly;
  bool? boldHintText;
  final Function(String)? onSubmitted;
  List<TextInputFormatter>? inputFormatters;
  final String? errorText;
  final int? maxLength;
  final Color? colorFill;
  bool? filled;

  CustomTextField(
      {super.key,
      this.controller,
      this.focusNode,
      this.style,
      this.inputBorder,
      this.contentPadding,
      this.onChanged,
      this.boldHintText,
      required this.autoFocus,
      required this.colorText,
      required this.colorHint,
      this.hinText,
      required this.autoCorrect,
      required this.radius,
      required this.fontSize,
      this.suffixIcon,
      this.showPassword = false,
      this.keyBoardType = TextInputType.text,
      this.maxLenght,
      this.maxLine = 1,
      this.textInputAction,
      this.readOnly,
      this.inputFormatters,
      this.onSubmitted,
      this.errorText,
      this.maxLength,
      this.colorFill,
      this.filled});

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      onChanged: onChanged,
      onFieldSubmitted: onSubmitted,
      controller: controller,
      focusNode: focusNode,
      readOnly: readOnly ?? false,
      autofocus: autoFocus,
      textAlignVertical: TextAlignVertical.center,
      autocorrect: autoCorrect,
      keyboardType: keyBoardType,
      textInputAction: textInputAction ?? TextInputAction.done,
      obscureText: showPassword,
      maxLength: maxLength,
      maxLines: maxLine,
      inputFormatters: inputFormatters,
      style: style ??
          TextStyle(
            color: colorText,
            fontSize: fontSize,
            fontWeight: FontWeight.w400,
          ),
      decoration: InputDecoration(
        filled: filled ?? false,
        fillColor: colorFill ?? Colors.white,
        contentPadding: contentPadding,
        isDense: true,
        hintText: hinText?.tr,
        hintStyle: AppTextStyle.s13Medium.copyWith(
          color: AppColors.black1E1E1E,
        ),
        suffixIcon: suffixIcon,
        errorText: errorText,
        errorMaxLines: 4,
        counterText: '',
        focusedErrorBorder: OutlineInputBorder(
          borderSide: const BorderSide(width: 1, color: AppColors.redCE3722),
          borderRadius: BorderRadius.circular(radius),
        ),
        errorBorder: OutlineInputBorder(
          borderSide: const BorderSide(width: 1, color: AppColors.redCE3722),
          borderRadius: BorderRadius.circular(radius),
        ),
        enabledBorder: inputBorder ??
            OutlineInputBorder(
              borderSide: const BorderSide(width: 1, color: AppColors.greyD9D9D9),
              borderRadius: BorderRadius.circular(radius),
            ),
        focusedBorder: inputBorder ??
            OutlineInputBorder(
              borderSide: const BorderSide(width: 1, color: AppColors.greyD9D9D9),
              borderRadius: BorderRadius.circular(radius),
            ),
      ),
    );
  }
}
